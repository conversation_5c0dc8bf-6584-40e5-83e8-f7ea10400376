{"version": 3, "file": "appStore.d.ts", "sourceRoot": "", "sources": ["../../../src/renderer/store/appStore.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,OAAO,EACP,UAAU,EACV,UAAU,EACV,UAAU,EACV,SAAS,EACV,MAAM,eAAe,CAAC;AAEvB,MAAM,WAAW,QAAQ;IAEvB,WAAW,EAAE,OAAO,CAAC;IACrB,YAAY,EAAE,OAAO,CAAC;IACtB,WAAW,EAAE,MAAM,GAAG,UAAU,GAAG,MAAM,CAAC;IAC1C,KAAK,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ,CAAC;IACnC,OAAO,EAAE,OAAO,CAAC;IAGjB,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC;IAG7B,aAAa,EAAE,YAAY,EAAE,CAAC;IAC9B,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,mBAAmB,EAAE,YAAY,GAAG,IAAI,CAAC;IAGzC,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAC9B,YAAY,EAAE,OAAO,CAAC;IACtB,WAAW,EAAE,SAAS,GAAG,IAAI,CAAC;IAC9B,WAAW,EAAE,UAAU,EAAE,CAAC;IAG1B,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAG9B,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAGrB,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,OAAO,CAAC;CACtB;AAED,MAAM,WAAW,UAAU;IAEzB,cAAc,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;IACxC,eAAe,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;IACzC,cAAc,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC;IACxD,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC;IAC7C,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAGvC,WAAW,EAAE,CAAC,QAAQ,EAAE,WAAW,KAAK,IAAI,CAAC;IAC7C,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC;IAGxD,gBAAgB,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,KAAK,IAAI,CAAC;IAC1D,eAAe,EAAE,CAAC,YAAY,EAAE,YAAY,KAAK,IAAI,CAAC;IACtD,kBAAkB,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC;IACzE,kBAAkB,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,IAAI,CAAC;IACzC,sBAAsB,EAAE,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC;IAGpD,UAAU,EAAE,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAC/D,aAAa,EAAE,CAAC,cAAc,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC;IAG9F,aAAa,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,IAAI,CAAC;IAC3C,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,IAAI,CAAC;IAC7C,cAAc,EAAE,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,KAAK,IAAI,CAAC;IACjD,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,KAAK,IAAI,CAAC;IAC5C,gBAAgB,EAAE,MAAM,IAAI,CAAC;IAC7B,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACtC,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACrC,cAAc,EAAE,MAAM,IAAI,CAAC;IAG3B,aAAa,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,IAAI,CAAC;IAG1C,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC;IACzC,UAAU,EAAE,MAAM,IAAI,CAAC;IAGvB,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/C,sBAAsB,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAChD,YAAY,EAAE,CAAC,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC;IAC3C,qBAAqB,EAAE,MAAM,IAAI,CAAC;IAGlC,oBAAoB,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAC9C,WAAW,EAAE,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/D,KAAK,EAAE,MAAM,IAAI,CAAC;CACnB;AA4BD,eAAO,MAAM,WAAW;;;;;;;;EAqLvB,CAAC;AAGF,eAAO,MAAM,kBAAkB,iBACkC,CAAC;AAElE,eAAO,MAAM,wBAAwB,eACc,CAAC;AAEpD,eAAO,MAAM,gBAAgB,0BACkD,CAAC;AAEhF,eAAO,MAAM,aAAa,eACmD,CAAC;AAE9E,eAAO,MAAM,WAAW,2CACwC,CAAC;AAEjE,eAAO,MAAM,QAAQ,cACuC,CAAC;AAG7D,eAAO,MAAM,uBAAuB,GAAI,UAAU,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,eAItE,CAAC;AAEJ,eAAO,MAAM,8BAA8B,GAAI,UAAU,CAAC,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,IAAI,eAIjG,CAAC;AAEJ,eAAO,MAAM,0BAA0B,GAAI,UAAU,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,KAAK,IAAI,eAIxF,CAAC"}