import { Base<PERSON><PERSON>rovider } from '../LLMProviderInterface';
import type { LLMProv<PERSON>Config, LLMResponse, StreamingLLMResponse, Message } from '@shared/types';
export declare class DeepSeekProvider extends BaseLL<PERSON>rovider {
    name: string;
    supportedModels: string[];
    chat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string): Promise<LLMResponse>;
    streamChat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string, onChunk?: (chunk: StreamingLLMResponse) => void): AsyncGenerator<StreamingLLMResponse, void, unknown>;
    countTokens(text: string, model: string): number;
    validateConfig(config: LLMProviderConfig): boolean;
}
//# sourceMappingURL=DeepSeekProvider.d.ts.map