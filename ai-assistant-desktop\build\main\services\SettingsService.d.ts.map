{"version": 3, "file": "SettingsService.d.ts", "sourceRoot": "", "sources": ["../../../src/main/services/SettingsService.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEpE,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D,qBAAa,eAAe;IAId,OAAO,CAAC,eAAe;IAHnC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAiC;IAChE,OAAO,CAAC,cAAc,CAA4B;gBAE9B,eAAe,EAAE,eAAe;IAE9C,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAK3B,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC;IAOnC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAW5D,UAAU,CAAC,CAAC,SAAS,MAAM,WAAW,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAKxE,UAAU,CAAC,CAAC,SAAS,MAAM,WAAW,EAC1C,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,GACpB,OAAO,CAAC,IAAI,CAAC;IAIV,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;IAM9B,YAAY,IAAI,OAAO,CAAC,iBAAiB,CAAC;IAK1C,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAIlE,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAO1D,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IASzD,QAAQ,IAAI,OAAO,CAAC,OAAO,GAAG,MAAM,GAAG,QAAQ,CAAC;IAKhD,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAM3D,WAAW,IAAI,OAAO,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;IAKpD,WAAW,CAAC,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAMlE,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAKlC,cAAc,CAAC,WAAW,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAOnD,gBAAgB,IAAI,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;IAK/C,gBAAgB,CAAC,IAAI,EAAE,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMzD,wBAAwB,IAAI,OAAO,CAAC,OAAO,CAAC;IAK5C,wBAAwB,CAAC,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAM1D,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC;IAKtC,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOrD,eAAe,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAKpC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAM/C,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAO3C,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAM5C,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAK/C,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMjE,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAK9C,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAS7D,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAajC,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyBzD,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,GAAG;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;YA+BpE,YAAY;IAW1B,OAAO,CAAC,yBAAyB;IA4DjC,OAAO,CAAC,oBAAoB;IAW5B,OAAO,CAAC,oBAAoB;IAgB5B,OAAO,CAAC,OAAO;IAIf,OAAO,CAAC,OAAO;IAKf,OAAO,CAAC,SAAS;IAuBjB,OAAO,CAAC,SAAS,CAAqD;IAEtE,gBAAgB,CAAC,CAAC,SAAS,MAAM,WAAW,EAC1C,GAAG,EAAE,CAAC,EACN,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GACxC,MAAM,IAAI;IAcb,OAAO,CAAC,kBAAkB;CAgB3B"}