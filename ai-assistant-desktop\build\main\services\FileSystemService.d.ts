import type { FileInfo, DiffResult } from '@shared/types';
export declare class FileSystemService {
    readFile(filePath: string): Promise<string>;
    writeFile(filePath: string, content: string): Promise<void>;
    listDirectory(dirPath: string): Promise<FileInfo[]>;
    globFiles(pattern: string, options?: {
        cwd?: string;
        absolute?: boolean;
        maxMatches?: number;
    }): Promise<string[]>;
    exists(filePath: string): Promise<boolean>;
    getStats(filePath: string): Promise<{
        size: number;
        isFile: boolean;
        isDirectory: boolean;
        modified: Date;
        created: Date;
    }>;
    deleteFile(filePath: string): Promise<void>;
    createDirectory(dirPath: string, recursive?: boolean): Promise<void>;
    copyFile(source: string, destination: string): Promise<void>;
    moveFile(source: string, destination: string): Promise<void>;
    createDiff(oldContent: string, newContent: string, oldPath?: string, newPath?: string): Promise<DiffResult>;
    replaceInFile(filePath: string, searchText: string, replaceText: string, options?: {
        regex?: boolean;
        global?: boolean;
        caseSensitive?: boolean;
        createBackup?: boolean;
    }): Promise<{
        success: boolean;
        replacements: number;
        preview?: DiffResult;
        backupPath?: string;
    }>;
    watchFile(filePath: string, callback: (eventType: string, filename?: string) => void): () => void;
}
//# sourceMappingURL=FileSystemService.d.ts.map