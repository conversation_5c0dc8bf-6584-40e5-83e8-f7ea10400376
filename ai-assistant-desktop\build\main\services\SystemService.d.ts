import * as os from 'os';
import type { SystemInfo } from '@shared/types';
export declare class SystemService {
    getSystemInfo(): Promise<SystemInfo>;
    executeCommand(command: string, options?: {
        cwd?: string;
        timeout?: number;
        shell?: boolean;
        env?: Record<string, string>;
    }): Promise<{
        stdout: string;
        stderr: string;
        exitCode: number;
        command: string;
        executionTime: number;
    }>;
    executeCommandStreaming(command: string, options?: {
        cwd?: string;
        shell?: boolean;
        env?: Record<string, string>;
        onStdout?: (data: string) => void;
        onStderr?: (data: string) => void;
        onExit?: (code: number) => void;
    }): Promise<{
        exitCode: number;
        command: string;
        executionTime: number;
    }>;
    getEnvironmentVariables(): Record<string, string>;
    setEnvironmentVariable(key: string, value: string): void;
    getCurrentWorkingDirectory(): string;
    changeWorkingDirectory(path: string): void;
    getProcessInfo(): {
        pid: number;
        ppid: number;
        platform: string;
        arch: string;
        version: string;
        memoryUsage: NodeJS.MemoryUsage;
        uptime: number;
    };
    getSystemResources(): Promise<{
        cpus: os.CpuInfo[];
        memory: {
            total: number;
            free: number;
            used: number;
            usage: number;
        };
        load: number[];
        uptime: number;
    }>;
    getNetworkInterfaces(): Promise<Record<string, os.NetworkInterfaceInfo[]>>;
    killProcess(pid: number, signal?: string): Promise<boolean>;
    listProcesses(): Promise<Array<{
        pid: number;
        name: string;
        cpu?: number;
        memory?: number;
    }>>;
    getSystemPath(): Promise<string[]>;
    findExecutable(name: string): Promise<string | null>;
    checkPortOpen(port: number, host?: string): Promise<boolean>;
    getUserInfo(): Promise<{
        username: string;
        homedir: string;
        shell?: string;
    }>;
    getDiskUsage(path?: string): Promise<{
        total: number;
        free: number;
        used: number;
        usage: number;
    } | null>;
}
//# sourceMappingURL=SystemService.d.ts.map