{"version": 3, "file": "react-syntax-highlighter_languages_highlight_tap.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/tap.js"], "sourcesContent": ["/*\nLanguage: Test Anything Protocol\nDescription: TAP, the Test Anything Protocol, is a simple text-based interface between testing modules in a test harness.\nRequires: yaml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://testanything.org\n*/\n\nfunction tap(hljs) {\n  return {\n    name: 'Test Anything Protocol',\n    case_insensitive: true,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      // version of format and total amount of testcases\n      {\n        className: 'meta',\n        variants: [\n          {\n            begin: '^TAP version (\\\\d+)$'\n          },\n          {\n            begin: '^1\\\\.\\\\.(\\\\d+)$'\n          }\n        ]\n      },\n      // YAML block\n      {\n        begin: /---$/,\n        end: '\\\\.\\\\.\\\\.$',\n        subLanguage: 'yaml',\n        relevance: 0\n      },\n      // testcase number\n      {\n        className: 'number',\n        begin: ' (\\\\d+) '\n      },\n      // testcase status and description\n      {\n        className: 'symbol',\n        variants: [\n          {\n            begin: '^ok'\n          },\n          {\n            begin: '^not ok'\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = tap;\n"], "names": [], "sourceRoot": ""}