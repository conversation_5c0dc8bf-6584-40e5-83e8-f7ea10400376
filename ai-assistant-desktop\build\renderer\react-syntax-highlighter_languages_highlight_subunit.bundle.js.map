{"version": 3, "file": "react-syntax-highlighter_languages_highlight_subunit.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/subunit.js"], "sourcesContent": ["/*\nLanguage: SubUnit\nAuthor: <PERSON> <serge<PERSON><EMAIL>>\nWebsite: https://pypi.org/project/python-subunit/\n*/\n\nfunction subunit(hljs) {\n  const DETAILS = {\n    className: 'string',\n    begin: '\\\\[\\n(multipart)?',\n    end: '\\\\]\\n'\n  };\n  const TIME = {\n    className: 'string',\n    begin: '\\\\d{4}-\\\\d{2}-\\\\d{2}(\\\\s+)\\\\d{2}:\\\\d{2}:\\\\d{2}\\.\\\\d+Z'\n  };\n  const PROGRESSVALUE = {\n    className: 'string',\n    begin: '(\\\\+|-)\\\\d+'\n  };\n  const KEYWORDS = {\n    className: 'keyword',\n    relevance: 10,\n    variants: [\n      {\n        begin: '^(test|testing|success|successful|failure|error|skip|xfail|uxsuccess)(:?)\\\\s+(test)?'\n      },\n      {\n        begin: '^progress(:?)(\\\\s+)?(pop|push)?'\n      },\n      {\n        begin: '^tags:'\n      },\n      {\n        begin: '^time:'\n      }\n    ]\n  };\n  return {\n    name: 'SubUnit',\n    case_insensitive: true,\n    contains: [\n      DETAILS,\n      TIME,\n      PROGRESSVALUE,\n      KEYWORDS\n    ]\n  };\n}\n\nmodule.exports = subunit;\n"], "names": [], "sourceRoot": ""}