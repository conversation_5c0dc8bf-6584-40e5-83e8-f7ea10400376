import type { AppSettings, LLMProviderConfig } from '@shared/types';
import { DatabaseService } from '../database/DatabaseService';
export declare class SettingsService {
    private databaseService;
    private readonly ENCRYPTION_KEY;
    private cachedSettings;
    constructor(databaseService: DatabaseService);
    initialize(): Promise<void>;
    getSettings(): Promise<AppSettings>;
    updateSettings(updates: Partial<AppSettings>): Promise<void>;
    getSetting<K extends keyof AppSettings>(key: K): Promise<AppSettings[K]>;
    setSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]): Promise<void>;
    resetSettings(): Promise<void>;
    getLLMConfig(): Promise<LLMProviderConfig>;
    updateLLMConfig(config: Partial<LLMProviderConfig>): Promise<void>;
    setAPIKey(provider: string, apiKey: string): Promise<void>;
    getAPIKey(provider?: string): Promise<string | undefined>;
    getTheme(): Promise<'light' | 'dark' | 'system'>;
    setTheme(theme: 'light' | 'dark' | 'system'): Promise<void>;
    getFontSize(): Promise<'small' | 'medium' | 'large'>;
    setFontSize(fontSize: 'small' | 'medium' | 'large'): Promise<void>;
    getCompactMode(): Promise<boolean>;
    setCompactMode(compactMode: boolean): Promise<void>;
    getExecutionMode(): Promise<'confirm' | 'yolo'>;
    setExecutionMode(mode: 'confirm' | 'yolo'): Promise<void>;
    getAutoSaveConversations(): Promise<boolean>;
    setAutoSaveConversations(autoSave: boolean): Promise<void>;
    getMaxContextLength(): Promise<number>;
    setMaxContextLength(maxLength: number): Promise<void>;
    getEnabledTools(): Promise<string[]>;
    setEnabledTools(tools: string[]): Promise<void>;
    enableTool(toolName: string): Promise<void>;
    disableTool(toolName: string): Promise<void>;
    getToolSettings(): Promise<Record<string, any>>;
    setToolSettings(toolSettings: Record<string, any>): Promise<void>;
    getToolSetting(toolName: string): Promise<any>;
    setToolSetting(toolName: string, setting: any): Promise<void>;
    exportSettings(): Promise<string>;
    importSettings(settingsJson: string): Promise<void>;
    validateLLMConfig(config: LLMProviderConfig): {
        valid: boolean;
        errors: string[];
    };
    private loadSettings;
    private validateSettingsStructure;
    private encryptSensitiveData;
    private decryptSensitiveData;
    private encrypt;
    private decrypt;
    private deepMerge;
    private listeners;
    onSettingChanged<K extends keyof AppSettings>(key: K, callback: (value: AppSettings[K]) => void): () => void;
    private emitSettingChanged;
}
//# sourceMappingURL=SettingsService.d.ts.map