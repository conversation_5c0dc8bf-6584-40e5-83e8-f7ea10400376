{"version": 3, "file": "react-syntax-highlighter_languages_highlight_pythonRepl.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/python-repl.js"], "sourcesContent": ["/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: [ 'pycon' ],\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'python'\n          }\n        },\n        variants: [\n          {\n            begin: /^>>>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = pythonRepl;\n"], "names": [], "sourceRoot": ""}