import type { ToolSchema, ToolResult } from '@shared/types';
export interface ToolDefinition {
    name: string;
    description: string;
    category: string;
    schema: ToolSchema;
    execute: (args: Record<string, any>) => Promise<any>;
}
export declare class ToolRegistry {
    private tools;
    private ajv;
    private toolsDirectory;
    constructor();
    initialize(): Promise<void>;
    private loadTools;
    private loadTool;
    private validateToolSchema;
    executeTool(name: string, args: Record<string, any>): Promise<ToolResult>;
    private validateArguments;
    private validateParameterType;
    getAvailableTools(): ToolDefinition[];
    getToolsByCategory(category: string): ToolDefinition[];
    getToolSchema(name: string): ToolSchema | null;
    getToolSchemas(): ToolSchema[];
    generateSystemPrompt(): string;
    private generateId;
    registerTool(toolDefinition: ToolDefinition): void;
    unregisterTool(name: string): boolean;
    isToolAvailable(name: string): boolean;
    getToolCategories(): string[];
    filterToolsBySettings(enabledTools: string[]): ToolDefinition[];
}
//# sourceMappingURL=ToolRegistry.d.ts.map