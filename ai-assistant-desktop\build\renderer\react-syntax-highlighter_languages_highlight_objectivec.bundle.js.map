{"version": 3, "file": "react-syntax-highlighter_languages_highlight_objectivec.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/objectivec.js"], "sourcesContent": ["/*\nLanguage: Objective-C\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://developer.apple.com/documentation/objectivec\nCategory: common\n*/\n\nfunction objectivec(hljs) {\n  const API_CLASS = {\n    className: 'built_in',\n    begin: '\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+'\n  };\n  const IDENTIFIER_RE = /[a-zA-Z@][a-zA-Z0-9_]*/;\n  const OBJC_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword:\n      'int float while char export sizeof typedef const struct for union ' +\n      'unsigned long volatile static bool mutable if do return goto void ' +\n      'enum else break extern asm case short default double register explicit ' +\n      'signed typename this switch continue wchar_t inline readonly assign ' +\n      'readwrite self @synchronized id typeof ' +\n      'nonatomic super unichar IBOutlet IBAction strong weak copy ' +\n      'in out inout bycopy byref oneway __strong __weak __block __autoreleasing ' +\n      '@private @protected @public @try @property @end @throw @catch @finally ' +\n      '@autoreleasepool @synthesize @dynamic @selector @optional @required ' +\n      '@encode @package @import @defs @compatibility_alias ' +\n      '__bridge __bridge_transfer __bridge_retained __bridge_retain ' +\n      '__covariant __contravariant __kindof ' +\n      '_Nonnull _Nullable _Null_unspecified ' +\n      '__FUNCTION__ __PRETTY_FUNCTION__ __attribute__ ' +\n      'getter setter retain unsafe_unretained ' +\n      'nonnull nullable null_unspecified null_resettable class instancetype ' +\n      'NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER ' +\n      'NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED ' +\n      'NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE ' +\n      'NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END ' +\n      'NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW ' +\n      'NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN',\n    literal:\n      'false true FALSE TRUE nil YES NO NULL',\n    built_in:\n      'BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once'\n  };\n  const CLASS_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: '@interface @class @protocol @implementation'\n  };\n  return {\n    name: 'Objective-C',\n    aliases: [\n      'mm',\n      'objc',\n      'obj-c',\n      'obj-c++',\n      'objective-c++'\n    ],\n    keywords: OBJC_KEYWORDS,\n    illegal: '</',\n    contains: [\n      API_CLASS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: '@\"',\n            end: '\"',\n            illegal: '\\\\n',\n            contains: [ hljs.BACKSLASH_ESCAPE ]\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: /#\\s*[a-z]+\\b/,\n        end: /$/,\n        keywords: {\n          'meta-keyword':\n            'if else elif endif define undef warning error line ' +\n            'pragma ifdef ifndef include'\n        },\n        contains: [\n          {\n            begin: /\\\\\\n/,\n            relevance: 0\n          },\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            className: 'meta-string'\n          }),\n          {\n            className: 'meta-string',\n            begin: /<.*?>/,\n            end: /$/,\n            illegal: '\\\\n'\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'class',\n        begin: '(' + CLASS_KEYWORDS.keyword.split(' ').join('|') + ')\\\\b',\n        end: /(\\{|$)/,\n        excludeEnd: true,\n        keywords: CLASS_KEYWORDS,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        begin: '\\\\.' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = objectivec;\n"], "names": [], "sourceRoot": ""}