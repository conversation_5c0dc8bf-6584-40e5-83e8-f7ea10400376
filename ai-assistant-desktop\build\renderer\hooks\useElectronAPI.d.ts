import type { AppSettings, Conversation, ToolResult } from '@shared/types';
export declare const useElectronAPI: () => import("../../main/preload").ElectronAPI;
export declare const useSettings: () => {
    loadSettings: () => Promise<void>;
    updateSettings: (updates: Partial<AppSettings>) => Promise<void>;
    resetSettings: () => Promise<void>;
    isLoading: boolean;
};
export declare const useConversations: () => {
    loadConversations: () => Promise<void>;
    createConversation: (title: string) => Promise<Conversation | null>;
    deleteConversation: (id: string) => Promise<boolean>;
    isLoading: boolean;
};
export declare const useSystemInfo: () => {
    loadSystemInfo: () => Promise<void>;
    isLoading: boolean;
};
export declare const useLLM: () => {
    sendMessage: (messages: any[], systemPrompt?: string) => Promise<string | null>;
    streamMessage: (messages: any[], systemPrompt?: string, onChunk?: (chunk: string) => void) => Promise<void>;
};
export declare const useAgent: () => {
    createPlan: (conversationId: string, executionMode?: "confirm" | "yolo") => Promise<any>;
    executePlan: (conversationId: string, stepId?: string) => Promise<any>;
    confirmExecution: (conversationId: string, approved: boolean) => Promise<boolean>;
};
export declare const useTools: () => {
    availableTools: any[];
    loadTools: () => Promise<void>;
    executeTool: (name: string, args: Record<string, any>) => Promise<ToolResult | null>;
};
export declare const useFileSystem: () => {
    readFile: (path: string) => Promise<any>;
    writeFile: (path: string, content: string) => Promise<boolean>;
    listDirectory: (path: string) => Promise<any>;
};
export declare const useWindowControls: () => {
    minimize: () => void;
    maximize: () => void;
    close: () => void;
};
//# sourceMappingURL=useElectronAPI.d.ts.map