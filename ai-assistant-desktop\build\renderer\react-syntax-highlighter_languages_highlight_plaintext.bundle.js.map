{"version": 3, "file": "react-syntax-highlighter_languages_highlight_plaintext.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/plaintext.js"], "sourcesContent": ["/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nmodule.exports = plaintext;\n"], "names": [], "sourceRoot": ""}