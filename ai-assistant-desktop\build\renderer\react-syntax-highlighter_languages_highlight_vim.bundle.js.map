{"version": 3, "file": "react-syntax-highlighter_languages_highlight_vim.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/vim.js"], "sourcesContent": ["/*\nLanguage: V<PERSON>\nAuthor: <PERSON> <<EMAIL>>\nDescription: full keyword and built-in from http://vimdoc.sourceforge.net/htmldoc/\nWebsite: https://www.vim.org\nCategory: scripting\n*/\n\nfunction vim(hljs) {\n  return {\n    name: '<PERSON><PERSON>',\n    keywords: {\n      $pattern: /[!#@\\w]+/,\n      keyword:\n        // express version except: ! & * < = > !! # @ @@\n        'N|0 P|0 X|0 a|0 ab abc abo al am an|0 ar arga argd arge argdo argg argl argu as au aug aun b|0 bN ba bad bd be bel bf bl bm bn bo bp br brea breaka breakd breakl bro bufdo buffers bun bw c|0 cN cNf ca cabc caddb cad caddf cal cat cb cc ccl cd ce cex cf cfir cgetb cgete cg changes chd che checkt cl cla clo cm cmapc cme cn cnew cnf cno cnorea cnoreme co col colo com comc comp con conf cope ' +\n        'cp cpf cq cr cs cst cu cuna cunme cw delm deb debugg delc delf dif diffg diffo diffp diffpu diffs diffthis dig di dl dell dj dli do doautoa dp dr ds dsp e|0 ea ec echoe echoh echom echon el elsei em en endfo endf endt endw ene ex exe exi exu f|0 files filet fin fina fini fir fix fo foldc foldd folddoc foldo for fu go gr grepa gu gv ha helpf helpg helpt hi hid his ia iabc if ij il im imapc ' +\n        'ime ino inorea inoreme int is isp iu iuna iunme j|0 ju k|0 keepa kee keepj lN lNf l|0 lad laddb laddf la lan lat lb lc lch lcl lcs le lefta let lex lf lfir lgetb lgete lg lgr lgrepa lh ll lla lli lmak lm lmapc lne lnew lnf ln loadk lo loc lockv lol lope lp lpf lr ls lt lu lua luad luaf lv lvimgrepa lw m|0 ma mak map mapc marks mat me menut mes mk mks mksp mkv mkvie mod mz mzf nbc nb nbs new nm nmapc nme nn nnoreme noa no noh norea noreme norm nu nun nunme ol o|0 om omapc ome on ono onoreme opt ou ounme ow p|0 ' +\n        'profd prof pro promptr pc ped pe perld po popu pp pre prev ps pt ptN ptf ptj ptl ptn ptp ptr pts pu pw py3 python3 py3d py3f py pyd pyf quita qa rec red redi redr redraws reg res ret retu rew ri rightb rub rubyd rubyf rund ru rv sN san sa sal sav sb sbN sba sbf sbl sbm sbn sbp sbr scrip scripte scs se setf setg setl sf sfir sh sim sig sil sl sla sm smap smapc sme sn sni sno snor snoreme sor ' +\n        'so spelld spe spelli spellr spellu spellw sp spr sre st sta startg startr star stopi stj sts sun sunm sunme sus sv sw sy synti sync tN tabN tabc tabdo tabe tabf tabfir tabl tabm tabnew ' +\n        'tabn tabo tabp tabr tabs tab ta tags tc tcld tclf te tf th tj tl tm tn to tp tr try ts tu u|0 undoj undol una unh unl unlo unm unme uns up ve verb vert vim vimgrepa vi viu vie vm vmapc vme vne vn vnoreme vs vu vunme windo w|0 wN wa wh wi winc winp wn wp wq wqa ws wu wv x|0 xa xmapc xm xme xn xnoreme xu xunme y|0 z|0 ~ ' +\n        // full version\n        'Next Print append abbreviate abclear aboveleft all amenu anoremenu args argadd argdelete argedit argglobal arglocal argument ascii autocmd augroup aunmenu buffer bNext ball badd bdelete behave belowright bfirst blast bmodified bnext botright bprevious brewind break breakadd breakdel breaklist browse bunload ' +\n        'bwipeout change cNext cNfile cabbrev cabclear caddbuffer caddexpr caddfile call catch cbuffer cclose center cexpr cfile cfirst cgetbuffer cgetexpr cgetfile chdir checkpath checktime clist clast close cmap cmapclear cmenu cnext cnewer cnfile cnoremap cnoreabbrev cnoremenu copy colder colorscheme command comclear compiler continue confirm copen cprevious cpfile cquit crewind cscope cstag cunmap ' +\n        'cunabbrev cunmenu cwindow delete delmarks debug debuggreedy delcommand delfunction diffupdate diffget diffoff diffpatch diffput diffsplit digraphs display deletel djump dlist doautocmd doautoall deletep drop dsearch dsplit edit earlier echo echoerr echohl echomsg else elseif emenu endif endfor ' +\n        'endfunction endtry endwhile enew execute exit exusage file filetype find finally finish first fixdel fold foldclose folddoopen folddoclosed foldopen function global goto grep grepadd gui gvim hardcopy help helpfind helpgrep helptags highlight hide history insert iabbrev iabclear ijump ilist imap ' +\n        'imapclear imenu inoremap inoreabbrev inoremenu intro isearch isplit iunmap iunabbrev iunmenu join jumps keepalt keepmarks keepjumps lNext lNfile list laddexpr laddbuffer laddfile last language later lbuffer lcd lchdir lclose lcscope left leftabove lexpr lfile lfirst lgetbuffer lgetexpr lgetfile lgrep lgrepadd lhelpgrep llast llist lmake lmap lmapclear lnext lnewer lnfile lnoremap loadkeymap loadview ' +\n        'lockmarks lockvar lolder lopen lprevious lpfile lrewind ltag lunmap luado luafile lvimgrep lvimgrepadd lwindow move mark make mapclear match menu menutranslate messages mkexrc mksession mkspell mkvimrc mkview mode mzscheme mzfile nbclose nbkey nbsart next nmap nmapclear nmenu nnoremap ' +\n        'nnoremenu noautocmd noremap nohlsearch noreabbrev noremenu normal number nunmap nunmenu oldfiles open omap omapclear omenu only onoremap onoremenu options ounmap ounmenu ownsyntax print profdel profile promptfind promptrepl pclose pedit perl perldo pop popup ppop preserve previous psearch ptag ptNext ' +\n        'ptfirst ptjump ptlast ptnext ptprevious ptrewind ptselect put pwd py3do py3file python pydo pyfile quit quitall qall read recover redo redir redraw redrawstatus registers resize retab return rewind right rightbelow ruby rubydo rubyfile rundo runtime rviminfo substitute sNext sandbox sargument sall saveas sbuffer sbNext sball sbfirst sblast sbmodified sbnext sbprevious sbrewind scriptnames scriptencoding ' +\n        'scscope set setfiletype setglobal setlocal sfind sfirst shell simalt sign silent sleep slast smagic smapclear smenu snext sniff snomagic snoremap snoremenu sort source spelldump spellgood spellinfo spellrepall spellundo spellwrong split sprevious srewind stop stag startgreplace startreplace ' +\n        'startinsert stopinsert stjump stselect sunhide sunmap sunmenu suspend sview swapname syntax syntime syncbind tNext tabNext tabclose tabedit tabfind tabfirst tablast tabmove tabnext tabonly tabprevious tabrewind tag tcl tcldo tclfile tearoff tfirst throw tjump tlast tmenu tnext topleft tprevious ' + 'trewind tselect tunmenu undo undojoin undolist unabbreviate unhide unlet unlockvar unmap unmenu unsilent update vglobal version verbose vertical vimgrep vimgrepadd visual viusage view vmap vmapclear vmenu vnew ' +\n        'vnoremap vnoremenu vsplit vunmap vunmenu write wNext wall while winsize wincmd winpos wnext wprevious wqall wsverb wundo wviminfo xit xall xmapclear xmap xmenu xnoremap xnoremenu xunmap xunmenu yank',\n      built_in: // built in func\n        'synIDtrans atan2 range matcharg did_filetype asin feedkeys xor argv ' +\n        'complete_check add getwinposx getqflist getwinposy screencol ' +\n        'clearmatches empty extend getcmdpos mzeval garbagecollect setreg ' +\n        'ceil sqrt diff_hlID inputsecret get getfperm getpid filewritable ' +\n        'shiftwidth max sinh isdirectory synID system inputrestore winline ' +\n        'atan visualmode inputlist tabpagewinnr round getregtype mapcheck ' +\n        'hasmapto histdel argidx findfile sha256 exists toupper getcmdline ' +\n        'taglist string getmatches bufnr strftime winwidth bufexists ' +\n        'strtrans tabpagebuflist setcmdpos remote_read printf setloclist ' +\n        'getpos getline bufwinnr float2nr len getcmdtype diff_filler luaeval ' +\n        'resolve libcallnr foldclosedend reverse filter has_key bufname ' +\n        'str2float strlen setline getcharmod setbufvar index searchpos ' +\n        'shellescape undofile foldclosed setqflist buflisted strchars str2nr ' +\n        'virtcol floor remove undotree remote_expr winheight gettabwinvar ' +\n        'reltime cursor tabpagenr finddir localtime acos getloclist search ' +\n        'tanh matchend rename gettabvar strdisplaywidth type abs py3eval ' +\n        'setwinvar tolower wildmenumode log10 spellsuggest bufloaded ' +\n        'synconcealed nextnonblank server2client complete settabwinvar ' +\n        'executable input wincol setmatches getftype hlID inputsave ' +\n        'searchpair or screenrow line settabvar histadd deepcopy strpart ' +\n        'remote_peek and eval getftime submatch screenchar winsaveview ' +\n        'matchadd mkdir screenattr getfontname libcall reltimestr getfsize ' +\n        'winnr invert pow getbufline byte2line soundfold repeat fnameescape ' +\n        'tagfiles sin strwidth spellbadword trunc maparg log lispindent ' +\n        'hostname setpos globpath remote_foreground getchar synIDattr ' +\n        'fnamemodify cscope_connection stridx winbufnr indent min ' +\n        'complete_add nr2char searchpairpos inputdialog values matchlist ' +\n        'items hlexists strridx browsedir expand fmod pathshorten line2byte ' +\n        'argc count getwinvar glob foldtextresult getreg foreground cosh ' +\n        'matchdelete has char2nr simplify histget searchdecl iconv ' +\n        'winrestcmd pumvisible writefile foldlevel haslocaldir keys cos ' +\n        'matchstr foldtext histnr tan tempname getcwd byteidx getbufvar ' +\n        'islocked escape eventhandler remote_send serverlist winrestview ' +\n        'synstack pyeval prevnonblank readfile cindent filereadable changenr ' +\n        'exp'\n    },\n    illegal: /;/,\n    contains: [\n      hljs.NUMBER_MODE,\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        illegal: '\\\\n'\n      },\n\n      /*\n      A double quote can start either a string or a line comment. Strings are\n      ended before the end of a line by another double quote and can contain\n      escaped double-quotes and post-escaped line breaks.\n\n      Also, any double quote at the beginning of a line is a comment but we\n      don't handle that properly at the moment: any double quote inside will\n      turn them into a string. Handling it properly will require a smarter\n      parser.\n      */\n      {\n        className: 'string',\n        begin: /\"(\\\\\"|\\n\\\\|[^\"\\n])*\"/\n      },\n      hljs.COMMENT('\"', '$'),\n\n      {\n        className: 'variable',\n        begin: /[bwtglsav]:[\\w\\d_]*/\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function function!',\n        end: '$',\n        relevance: 0,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: '\\\\(',\n            end: '\\\\)'\n          }\n        ]\n      },\n      {\n        className: 'symbol',\n        begin: /<[\\w-]+>/\n      }\n    ]\n  };\n}\n\nmodule.exports = vim;\n"], "names": [], "sourceRoot": ""}