(global["webpackChunkai_assistant_desktop"] = global["webpackChunkai_assistant_desktop"] || []).push([["react-syntax-highlighter_languages_highlight_plaintext"],{

/***/ "./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/plaintext.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/plaintext.js ***!
  \****************************************************************************************************/
/***/ ((module) => {

/*
Language: Plain text
Author: <PERSON><PERSON> (<EMAIL>)
Description: Plain text without any highlighting.
Category: common
*/

function plaintext(hljs) {
  return {
    name: 'Plain text',
    aliases: [
      'text',
      'txt'
    ],
    disableAutodetect: true
  };
}

module.exports = plaintext;


/***/ })

}]);
//# sourceMappingURL=react-syntax-highlighter_languages_highlight_plaintext.bundle.js.map