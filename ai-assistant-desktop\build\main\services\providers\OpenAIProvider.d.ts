import { BaseLL<PERSON>rovider } from '../LLMProviderInterface';
import type { LLMProviderConfig, LLMResponse, StreamingLLMResponse, Message } from '@shared/types';
export declare class OpenAIProvider extends BaseLLMProvider {
    name: string;
    supportedModels: string[];
    chat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string): Promise<LLMResponse>;
    streamChat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string, onChunk?: (chunk: StreamingLLMResponse) => void): AsyncGenerator<StreamingLLMResponse, void, unknown>;
    countTokens(text: string, model: string): number;
    validateConfig(config: LLMProviderConfig): boolean;
}
//# sourceMappingURL=OpenAIProvider.d.ts.map