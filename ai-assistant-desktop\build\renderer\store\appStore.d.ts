import type { AppSettings, Conversation, Message, AgentState, SystemInfo, ToolResult, AgentPlan } from '@shared/types';
export interface AppState {
    sidebarOpen: boolean;
    settingsOpen: boolean;
    currentView: 'chat' | 'settings' | 'logs';
    theme: 'light' | 'dark' | 'system';
    loading: boolean;
    settings: AppSettings | null;
    conversations: Conversation[];
    currentConversationId: string | null;
    currentConversation: Conversation | null;
    agentState: AgentState | null;
    isProcessing: boolean;
    currentPlan: AgentPlan | null;
    toolResults: ToolResult[];
    systemInfo: SystemInfo | null;
    error: string | null;
    streamingMessage: string;
    isStreaming: boolean;
}
export interface AppActions {
    setSidebarOpen: (open: boolean) => void;
    setSettingsOpen: (open: boolean) => void;
    setCurrentView: (view: AppState['currentView']) => void;
    setTheme: (theme: AppState['theme']) => void;
    setLoading: (loading: boolean) => void;
    setSettings: (settings: AppSettings) => void;
    updateSettings: (updates: Partial<AppSettings>) => void;
    setConversations: (conversations: Conversation[]) => void;
    addConversation: (conversation: Conversation) => void;
    updateConversation: (id: string, updates: Partial<Conversation>) => void;
    deleteConversation: (id: string) => void;
    setCurrentConversation: (id: string | null) => void;
    addMessage: (conversationId: string, message: Message) => void;
    updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => void;
    setAgentState: (state: AgentState) => void;
    setProcessing: (processing: boolean) => void;
    setCurrentPlan: (plan: AgentPlan | null) => void;
    addToolResult: (result: ToolResult) => void;
    clearToolResults: () => void;
    confirmPlan: (planId: string) => void;
    rejectPlan: (planId: string) => void;
    stopGeneration: () => void;
    setSystemInfo: (info: SystemInfo) => void;
    setError: (error: string | null) => void;
    clearError: () => void;
    setStreamingMessage: (message: string) => void;
    appendStreamingMessage: (chunk: string) => void;
    setStreaming: (streaming: boolean) => void;
    clearStreamingMessage: () => void;
    startNewConversation: (title: string) => void;
    sendMessage: (conversationId: string, content: string) => void;
    reset: () => void;
}
export declare const useAppStore: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<AppState & AppActions>, "subscribe"> & {
    subscribe: {
        (listener: (selectedState: AppState & AppActions, previousSelectedState: AppState & AppActions) => void): () => void;
        <U>(selector: (state: AppState & AppActions) => U, listener: (selectedState: U, previousSelectedState: U) => void, options?: {
            equalityFn?: ((a: U, b: U) => boolean) | undefined;
            fireImmediately?: boolean;
        } | undefined): () => void;
    };
}>;
export declare const useCurrentMessages: () => Message[];
export declare const useHasActiveConversation: () => boolean;
export declare const useExecutionMode: () => "confirm" | "yolo";
export declare const useIsYoloMode: () => boolean;
export declare const useProvider: () => "openai" | "anthropic" | "deepseek";
export declare const useModel: () => string;
export declare const subscribeToThemeChanges: (callback: (theme: string) => void) => () => void;
export declare const subscribeToConversationChanges: (callback: (conversation: Conversation | null) => void) => () => void;
export declare const subscribeToSettingsChanges: (callback: (settings: AppSettings | null) => void) => () => void;
//# sourceMappingURL=appStore.d.ts.map