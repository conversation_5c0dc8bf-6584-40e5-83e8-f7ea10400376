import type { LLMProviderConfig, LLMResponse, StreamingLLMResponse, Message } from '@shared/types';
export declare class LLMService {
    private providers;
    constructor();
    private initializeProviders;
    private getProvider;
    chat(request: {
        messages: Message[];
        systemPrompt?: string;
    }, config: LLMProviderConfig): Promise<LLMResponse>;
    streamChat(request: {
        messages: Message[];
        systemPrompt?: string;
    }, config: LLMProviderConfig): Promise<AsyncGenerator<StreamingLLMResponse, void, unknown>>;
    countTokens(text: string, config: LLMProviderConfig): number;
    getAvailableProviders(): string[];
    getProviderModels(providerName: string): string[];
    validateProviderConfig(config: LLMProviderConfig): boolean;
    optimizeContextWindow(messages: Message[], config: LLMProviderConfig, maxTokens?: number): Message[];
    summarizeContext(messages: Message[], config: LLMProviderConfig, summaryTarget?: number): Promise<Message[]>;
}
//# sourceMappingURL=LLMService.d.ts.map