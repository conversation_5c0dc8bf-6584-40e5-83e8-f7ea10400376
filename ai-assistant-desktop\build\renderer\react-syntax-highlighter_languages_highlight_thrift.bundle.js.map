{"version": 3, "file": "react-syntax-highlighter_languages_highlight_thrift.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/thrift.js"], "sourcesContent": ["/*\nLanguage: Thrift\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Thrift message definition format\nWebsite: https://thrift.apache.org\nCategory: protocols\n*/\n\nfunction thrift(hljs) {\n  const BUILT_IN_TYPES = 'bool byte i16 i32 i64 double string binary';\n  return {\n    name: 'Thrift',\n    keywords: {\n      keyword:\n        'namespace const typedef struct enum service exception void oneway set list map required optional',\n      built_in:\n        BUILT_IN_TYPES,\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'struct enum service exception',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            // hack: eating everything after the first title\n            starts: {\n              endsWithParent: true,\n              excludeEnd: true\n            }\n          })\n        ]\n      },\n      {\n        begin: '\\\\b(set|list|map)\\\\s*<',\n        end: '>',\n        keywords: BUILT_IN_TYPES,\n        contains: [ 'self' ]\n      }\n    ]\n  };\n}\n\nmodule.exports = thrift;\n"], "names": [], "sourceRoot": ""}