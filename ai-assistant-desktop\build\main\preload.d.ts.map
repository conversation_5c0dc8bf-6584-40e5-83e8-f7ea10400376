{"version": 3, "file": "preload.d.ts", "sourceRoot": "", "sources": ["../../src/main/preload.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAc,WAAW,EAAE,MAAM,eAAe,CAAC;AAyG7D,MAAM,WAAW,WAAW;IAC1B,GAAG,EAAE;QACH,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC7C,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;KAClC,CAAC;IACF,KAAK,EAAE;QACL,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC7C,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;QACjC,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;KACjD,CAAC;IACF,KAAK,EAAE;QACL,IAAI,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;QACjC,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;KACjD,CAAC;IACF,aAAa,EAAE;QACb,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;QACjC,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;KAC7C,CAAC;IACF,QAAQ,EAAE;QACR,GAAG,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;QAChC,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC5C,KAAK,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;KACnC,CAAC;IACF,EAAE,EAAE;QACF,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC7C,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;KAC9C,CAAC;IACF,MAAM,EAAE;QACN,IAAI,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;QACjC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;KAC/C,CAAC;IACF,MAAM,EAAE;QACN,QAAQ,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,QAAQ,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,KAAK,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;KAC5B,CAAC;IACF,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IAClD,kBAAkB,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CAC/C;AAED,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,MAAM;QACd,WAAW,EAAE,WAAW,CAAC;KAC1B;CACF"}