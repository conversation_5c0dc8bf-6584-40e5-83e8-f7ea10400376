{"version": 3, "file": "react-syntax-highlighter_languages_highlight_latex.bundle.js", "mappings": ";;;;;;;;AAAA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;;AAEA;AACA,WAAW,kBAAkB;AAC7B,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,sBAAsB;AACjC,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,IAAI;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,GAAG;AAC1B;AACA;AACA;AACA,gDAAgD,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,oBAAoB;AACzB,KAAK,sBAAsB;AAC3B;AACA;AACA,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE;AAC/B,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE;AAC/B,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE;AAC/B,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE;AAC/B,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE;AAC/B,KAAK,WAAW,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,WAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,gDAAgD;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,kBAAkB;AACvE,mBAAmB,4CAA4C;AAC/D;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,yBAAyB,kBAAkB;AAC3C;AACA;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA,0BAA0B;AAC1B,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,uCAAuC;AAClG,kDAAkD,uCAAuC;AACzF,wDAAwD,oEAAoE;AAC5H,mBAAmB,iFAAiF;AACpG,wBAAwB,8CAA8C;AACtE,kDAAkD,8CAA8C;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/latex.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: LaTeX\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.latex-project.org\nCategory: markup\n*/\n\n/** @type LanguageFn */\nfunction latex(hljs) {\n  const KNOWN_CONTROL_WORDS = either(...[\n      '(?:NeedsTeXFormat|RequirePackage|GetIdInfo)',\n      'Provides(?:Expl)?(?:Package|Class|File)',\n      '(?:DeclareOption|ProcessOptions)',\n      '(?:documentclass|usepackage|input|include)',\n      'makeat(?:letter|other)',\n      'ExplSyntax(?:On|Off)',\n      '(?:new|renew|provide)?command',\n      '(?:re)newenvironment',\n      '(?:New|Renew|Provide|Declare)(?:Expandable)?DocumentCommand',\n      '(?:New|Renew|Provide|Declare)DocumentEnvironment',\n      '(?:(?:e|g|x)?def|let)',\n      '(?:begin|end)',\n      '(?:part|chapter|(?:sub){0,2}section|(?:sub)?paragraph)',\n      'caption',\n      '(?:label|(?:eq|page|name)?ref|(?:paren|foot|super)?cite)',\n      '(?:alpha|beta|[Gg]amma|[Dd]elta|(?:var)?epsilon|zeta|eta|[Tt]heta|vartheta)',\n      '(?:iota|(?:var)?kappa|[Ll]ambda|mu|nu|[Xx]i|[Pp]i|varpi|(?:var)rho)',\n      '(?:[Ss]igma|varsigma|tau|[Uu]psilon|[Pp]hi|varphi|chi|[Pp]si|[Oo]mega)',\n      '(?:frac|sum|prod|lim|infty|times|sqrt|leq|geq|left|right|middle|[bB]igg?)',\n      '(?:[lr]angle|q?quad|[lcvdi]?dots|d?dot|hat|tilde|bar)'\n    ].map(word => word + '(?![a-zA-Z@:_])'));\n  const L3_REGEX = new RegExp([\n      // A function \\module_function_name:signature or \\__module_function_name:signature,\n      // where both module and function_name need at least two characters and\n      // function_name may contain single underscores.\n      '(?:__)?[a-zA-Z]{2,}_[a-zA-Z](?:_?[a-zA-Z])+:[a-zA-Z]*',\n      // A variable \\scope_module_and_name_type or \\scope__module_ane_name_type,\n      // where scope is one of l, g or c, type needs at least two characters\n      // and module_and_name may contain single underscores.\n      '[lgc]__?[a-zA-Z](?:_?[a-zA-Z])*_[a-zA-Z]{2,}',\n      // A quark \\q_the_name or \\q__the_name or\n      // scan mark \\s_the_name or \\s__vthe_name,\n      // where variable_name needs at least two characters and\n      // may contain single underscores.\n      '[qs]__?[a-zA-Z](?:_?[a-zA-Z])+',\n      // Other LaTeX3 macro names that are not covered by the three rules above.\n      'use(?:_i)?:[a-zA-Z]*',\n      '(?:else|fi|or):',\n      '(?:if|cs|exp):w',\n      '(?:hbox|vbox):n',\n      '::[a-zA-Z]_unbraced',\n      '::[a-zA-Z:]'\n    ].map(pattern => pattern + '(?![a-zA-Z:_])').join('|'));\n  const L2_VARIANTS = [\n    {begin: /[a-zA-Z@]+/}, // control word\n    {begin: /[^a-zA-Z@]?/} // control symbol\n  ];\n  const DOUBLE_CARET_VARIANTS = [\n    {begin: /\\^{6}[0-9a-f]{6}/},\n    {begin: /\\^{5}[0-9a-f]{5}/},\n    {begin: /\\^{4}[0-9a-f]{4}/},\n    {begin: /\\^{3}[0-9a-f]{3}/},\n    {begin: /\\^{2}[0-9a-f]{2}/},\n    {begin: /\\^{2}[\\u0000-\\u007f]/}\n  ];\n  const CONTROL_SEQUENCE = {\n    className: 'keyword',\n    begin: /\\\\/,\n    relevance: 0,\n    contains: [\n      {\n        endsParent: true,\n        begin: KNOWN_CONTROL_WORDS\n      },\n      {\n        endsParent: true,\n        begin: L3_REGEX\n      },\n      {\n        endsParent: true,\n        variants: DOUBLE_CARET_VARIANTS\n      },\n      {\n        endsParent: true,\n        relevance: 0,\n        variants: L2_VARIANTS\n      }\n    ]\n  };\n  const MACRO_PARAM = {\n    className: 'params',\n    relevance: 0,\n    begin: /#+\\d?/\n  };\n  const DOUBLE_CARET_CHAR = {\n    // relevance: 1\n    variants: DOUBLE_CARET_VARIANTS\n  };\n  const SPECIAL_CATCODE = {\n    className: 'built_in',\n    relevance: 0,\n    begin: /[$&^_]/\n  };\n  const MAGIC_COMMENT = {\n    className: 'meta',\n    begin: '% !TeX',\n    end: '$',\n    relevance: 10\n  };\n  const COMMENT = hljs.COMMENT(\n    '%',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  const EVERYTHING_BUT_VERBATIM = [\n    CONTROL_SEQUENCE,\n    MACRO_PARAM,\n    DOUBLE_CARET_CHAR,\n    SPECIAL_CATCODE,\n    MAGIC_COMMENT,\n    COMMENT\n  ];\n  const BRACE_GROUP_NO_VERBATIM = {\n    begin: /\\{/, end: /\\}/,\n    relevance: 0,\n    contains: ['self', ...EVERYTHING_BUT_VERBATIM]\n  };\n  const ARGUMENT_BRACES = hljs.inherit(\n    BRACE_GROUP_NO_VERBATIM,\n    {\n      relevance: 0,\n      endsParent: true,\n      contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n    }\n  );\n  const ARGUMENT_BRACKETS = {\n    begin: /\\[/,\n      end: /\\]/,\n    endsParent: true,\n    relevance: 0,\n    contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  };\n  const SPACE_GOBBLER = {\n    begin: /\\s+/,\n    relevance: 0\n  };\n  const ARGUMENT_M = [ARGUMENT_BRACES];\n  const ARGUMENT_O = [ARGUMENT_BRACKETS];\n  const ARGUMENT_AND_THEN = function(arg, starts_mode) {\n    return {\n      contains: [SPACE_GOBBLER],\n      starts: {\n        relevance: 0,\n        contains: arg,\n        starts: starts_mode\n      }\n    };\n  };\n  const CSNAME = function(csname, starts_mode) {\n    return {\n        begin: '\\\\\\\\' + csname + '(?![a-zA-Z@:_])',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\' + csname},\n        relevance: 0,\n        contains: [SPACE_GOBBLER],\n        starts: starts_mode\n      };\n  };\n  const BEGIN_ENV = function(envname, starts_mode) {\n    return hljs.inherit(\n      {\n        begin: '\\\\\\\\begin(?=[ \\t]*(\\\\r?\\\\n[ \\t]*)?\\\\{' + envname + '\\\\})',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\begin'},\n        relevance: 0,\n      },\n      ARGUMENT_AND_THEN(ARGUMENT_M, starts_mode)\n    );\n  };\n  const VERBATIM_DELIMITED_EQUAL = (innerName = \"string\") => {\n    return hljs.END_SAME_AS_BEGIN({\n      className: innerName,\n      begin: /(.|\\r?\\n)/,\n      end: /(.|\\r?\\n)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsParent: true\n    });\n  };\n  const VERBATIM_DELIMITED_ENV = function(envname) {\n    return {\n      className: 'string',\n      end: '(?=\\\\\\\\end\\\\{' + envname + '\\\\})'\n    };\n  };\n\n  const VERBATIM_DELIMITED_BRACES = (innerName = \"string\") => {\n    return {\n      relevance: 0,\n      begin: /\\{/,\n      starts: {\n        endsParent: true,\n        contains: [\n          {\n            className: innerName,\n            end: /(?=\\})/,\n            endsParent:true,\n            contains: [\n              {\n                begin: /\\{/,\n                end: /\\}/,\n                relevance: 0,\n                contains: [\"self\"]\n              }\n            ],\n          }\n        ]\n      }\n    };\n  };\n  const VERBATIM = [\n    ...['verb', 'lstinline'].map(csname => CSNAME(csname, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mint', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mintinline', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_BRACES(), VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('url', {contains: [VERBATIM_DELIMITED_BRACES(\"link\"), VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('hyperref', {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('href', ARGUMENT_AND_THEN(ARGUMENT_O, {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]})),\n    ...[].concat(...['', '\\\\*'].map(suffix => [\n      BEGIN_ENV('verbatim' + suffix, VERBATIM_DELIMITED_ENV('verbatim' + suffix)),\n      BEGIN_ENV('filecontents' + suffix,  ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('filecontents' + suffix))),\n      ...['', 'B', 'L'].map(prefix =>\n        BEGIN_ENV(prefix + 'Verbatim' + suffix, ARGUMENT_AND_THEN(ARGUMENT_O, VERBATIM_DELIMITED_ENV(prefix + 'Verbatim' + suffix)))\n      )\n    ])),\n    BEGIN_ENV('minted', ARGUMENT_AND_THEN(ARGUMENT_O, ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('minted')))),\n  ];\n\n  return {\n    name: 'LaTeX',\n    aliases: ['tex'],\n    contains: [\n      ...VERBATIM,\n      ...EVERYTHING_BUT_VERBATIM\n    ]\n  };\n}\n\nmodule.exports = latex;\n"], "names": [], "sourceRoot": ""}