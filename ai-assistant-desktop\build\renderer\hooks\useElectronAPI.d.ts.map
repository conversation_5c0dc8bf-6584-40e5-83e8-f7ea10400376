{"version": 3, "file": "useElectronAPI.d.ts", "sourceRoot": "", "sources": ["../../../src/renderer/hooks/useElectronAPI.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAe,WAAW,EAAE,YAAY,EAAc,UAAU,EAAE,MAAM,eAAe,CAAC;AAIpG,eAAO,MAAM,cAAc,gDAQ1B,CAAC;AAGF,eAAO,MAAM,WAAW;;8BAqB6B,OAAO,CAAC,WAAW,CAAC;;;CAsCxE,CAAC;AAGF,eAAO,MAAM,gBAAgB;;gCAqB0B,MAAM;6BAgBT,MAAM;;CAsBzD,CAAC;AAGF,eAAO,MAAM,aAAa;;;CAyBzB,CAAC;AAGF,eAAO,MAAM,MAAM;4BAWL,GAAG,EAAE,iBACA,MAAM,KACpB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;8BAgBb,GAAG,EAAE,iBACA,MAAM,YACX,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,KAChC,OAAO,CAAC,IAAI,CAAC;CAwBjB,CAAC;AAGF,eAAO,MAAM,QAAQ;iCASmC,MAAM,kBAAkB,SAAS,GAAG,MAAM;kCAmBzC,MAAM,WAAW,MAAM;uCAsBlB,MAAM,YAAY,OAAO;CAoBtF,CAAC;AAGF,eAAO,MAAM,QAAQ;;;wBAkB0B,MAAM,QAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAwB/E,CAAC;AAGF,eAAO,MAAM,aAAa;qBAIkB,MAAM;sBAeL,MAAM,WAAW,MAAM;0BAenB,MAAM;CAoBtD,CAAC;AAGF,eAAO,MAAM,iBAAiB;;;;CAoB7B,CAAC"}