import { Base<PERSON><PERSON>rovider } from '../LLMProviderInterface';
import type { LLMProviderConfig, LLMResponse, StreamingLLMResponse, Message } from '@shared/types';
export declare class AnthropicProvider extends BaseLLMProvider {
    name: string;
    supportedModels: string[];
    protected buildHeaders(config: LLMProviderConfig): Record<string, string>;
    protected formatMessages(messages: Message[], systemPrompt?: string): any;
    chat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string): Promise<LLMResponse>;
    streamChat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string, onChunk?: (chunk: StreamingLLMResponse) => void): AsyncGenerator<StreamingLLMResponse, void, unknown>;
    countTokens(text: string, model: string): number;
    validateConfig(config: LLMProviderConfig): boolean;
}
//# sourceMappingURL=AnthropicProvider.d.ts.map