import type { APIResponse } from '@shared/types';
export interface ElectronAPI {
    llm: {
        chat: (request: any) => Promise<APIResponse>;
        stream: (request: any) => Promise<APIResponse>;
        stop: () => Promise<APIResponse>;
    };
    agent: {
        plan: (request: any) => Promise<APIResponse>;
        execute: (request: any) => Promise<APIResponse>;
        stop: () => Promise<APIResponse>;
        confirm: (request: any) => Promise<APIResponse>;
    };
    tools: {
        list: () => Promise<APIResponse>;
        execute: (request: any) => Promise<APIResponse>;
    };
    conversations: {
        create: (request: any) => Promise<APIResponse>;
        update: (request: any) => Promise<APIResponse>;
        delete: (request: any) => Promise<APIResponse>;
        list: () => Promise<APIResponse>;
        get: (request: any) => Promise<APIResponse>;
    };
    settings: {
        get: () => Promise<APIResponse>;
        set: (request: any) => Promise<APIResponse>;
        reset: () => Promise<APIResponse>;
    };
    fs: {
        read: (request: any) => Promise<APIResponse>;
        write: (request: any) => Promise<APIResponse>;
        list: (request: any) => Promise<APIResponse>;
        glob: (request: any) => Promise<APIResponse>;
    };
    system: {
        info: () => Promise<APIResponse>;
        shell: (request: any) => Promise<APIResponse>;
    };
    window: {
        minimize: () => Promise<void>;
        maximize: () => Promise<void>;
        close: () => Promise<void>;
    };
    on: (channel: string, callback: Function) => void;
    removeAllListeners: (channel: string) => void;
}
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
//# sourceMappingURL=preload.d.ts.map