import type { ToolSchema } from '@shared/types';
export declare const category: "search";
export declare const schema: ToolSchema;
interface MatchResult {
    file: string;
    line: number;
    column: number;
    match: string;
    context?: {
        before: string[];
        after: string[];
    };
}
interface SearchResult {
    pattern: string;
    searchPath: string;
    matches: MatchResult[];
    filesSearched: number;
    totalMatches: number;
    executionTime: number;
}
export declare function execute(args: {
    pattern: string;
    path: string;
    recursive?: boolean;
    caseSensitive?: boolean;
    contextLines?: number;
    maxMatches?: number;
    filePattern?: string;
    excludePattern?: string;
}): Promise<SearchResult>;
export declare function searchText(args: {
    text: string;
    path: string;
    recursive?: boolean;
    caseSensitive?: boolean;
}): Promise<SearchResult>;
export {};
//# sourceMappingURL=grep.d.ts.map