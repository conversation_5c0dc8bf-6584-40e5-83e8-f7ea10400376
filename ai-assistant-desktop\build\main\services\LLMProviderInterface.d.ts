import type { LLMProviderConfig, LLMResponse, StreamingLLMResponse, Message } from '@shared/types';
export interface LLMProviderInterface {
    name: string;
    supportedModels: string[];
    initialize(config: LLMProviderConfig): Promise<void>;
    chat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string): Promise<LLMResponse>;
    streamChat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string, onChunk?: (chunk: StreamingLLMResponse) => void): AsyncGenerator<StreamingLLMResponse, void, unknown>;
    countTokens(text: string, model: string): number;
    validateConfig(config: LLMProviderConfig): boolean;
}
export declare abstract class BaseLL<PERSON>rovider implements LLMProviderInterface {
    abstract name: string;
    abstract supportedModels: string[];
    protected config?: LLMProviderConfig;
    initialize(config: LLMProviderConfig): Promise<void>;
    abstract chat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string): Promise<LLMResponse>;
    abstract streamChat(messages: Message[], config: LLMProviderConfig, systemPrompt?: string, onChunk?: (chunk: StreamingLLMResponse) => void): AsyncGenerator<StreamingLLMResponse, void, unknown>;
    abstract countTokens(text: string, model: string): number;
    validateConfig(config: LLMProviderConfig): boolean;
    protected buildHeaders(config: LLMProviderConfig): Record<string, string>;
    protected formatMessages(messages: Message[], systemPrompt?: string): any[];
}
//# sourceMappingURL=LLMProviderInterface.d.ts.map