{"version": 3, "file": "react-syntax-highlighter_languages_highlight_verilog.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/verilog.js"], "sourcesContent": ["/*\nLanguage: Verilog\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nDescription: Verilog is a hardware description language used in electronic design automation to describe digital and mixed-signal systems. This highlighter supports Verilog and SystemVerilog through IEEE 1800-2012.\nWebsite: http://www.verilog.com\n*/\n\nfunction verilog(hljs) {\n  const SV_KEYWORDS = {\n    $pattern: /[\\w\\$]+/,\n    keyword:\n      'accept_on alias always always_comb always_ff always_latch and assert assign ' +\n      'assume automatic before begin bind bins binsof bit break buf|0 bufif0 bufif1 ' +\n      'byte case casex casez cell chandle checker class clocking cmos config const ' +\n      'constraint context continue cover covergroup coverpoint cross deassign default ' +\n      'defparam design disable dist do edge else end endcase endchecker endclass ' +\n      'endclocking endconfig endfunction endgenerate endgroup endinterface endmodule ' +\n      'endpackage endprimitive endprogram endproperty endspecify endsequence endtable ' +\n      'endtask enum event eventually expect export extends extern final first_match for ' +\n      'force foreach forever fork forkjoin function generate|5 genvar global highz0 highz1 ' +\n      'if iff ifnone ignore_bins illegal_bins implements implies import incdir include ' +\n      'initial inout input inside instance int integer interconnect interface intersect ' +\n      'join join_any join_none large let liblist library local localparam logic longint ' +\n      'macromodule matches medium modport module nand negedge nettype new nexttime nmos ' +\n      'nor noshowcancelled not notif0 notif1 or output package packed parameter pmos ' +\n      'posedge primitive priority program property protected pull0 pull1 pulldown pullup ' +\n      'pulsestyle_ondetect pulsestyle_onevent pure rand randc randcase randsequence rcmos ' +\n      'real realtime ref reg reject_on release repeat restrict return rnmos rpmos rtran ' +\n      'rtranif0 rtranif1 s_always s_eventually s_nexttime s_until s_until_with scalared ' +\n      'sequence shortint shortreal showcancelled signed small soft solve specify specparam ' +\n      'static string strong strong0 strong1 struct super supply0 supply1 sync_accept_on ' +\n      'sync_reject_on table tagged task this throughout time timeprecision timeunit tran ' +\n      'tranif0 tranif1 tri tri0 tri1 triand trior trireg type typedef union unique unique0 ' +\n      'unsigned until until_with untyped use uwire var vectored virtual void wait wait_order ' +\n      'wand weak weak0 weak1 while wildcard wire with within wor xnor xor',\n    literal:\n      'null',\n    built_in:\n      '$finish $stop $exit $fatal $error $warning $info $realtime $time $printtimescale ' +\n      '$bitstoreal $bitstoshortreal $itor $signed $cast $bits $stime $timeformat ' +\n      '$realtobits $shortrealtobits $rtoi $unsigned $asserton $assertkill $assertpasson ' +\n      '$assertfailon $assertnonvacuouson $assertoff $assertcontrol $assertpassoff ' +\n      '$assertfailoff $assertvacuousoff $isunbounded $sampled $fell $changed $past_gclk ' +\n      '$fell_gclk $changed_gclk $rising_gclk $steady_gclk $coverage_control ' +\n      '$coverage_get $coverage_save $set_coverage_db_name $rose $stable $past ' +\n      '$rose_gclk $stable_gclk $future_gclk $falling_gclk $changing_gclk $display ' +\n      '$coverage_get_max $coverage_merge $get_coverage $load_coverage_db $typename ' +\n      '$unpacked_dimensions $left $low $increment $clog2 $ln $log10 $exp $sqrt $pow ' +\n      '$floor $ceil $sin $cos $tan $countbits $onehot $isunknown $fatal $warning ' +\n      '$dimensions $right $high $size $asin $acos $atan $atan2 $hypot $sinh $cosh ' +\n      '$tanh $asinh $acosh $atanh $countones $onehot0 $error $info $random ' +\n      '$dist_chi_square $dist_erlang $dist_exponential $dist_normal $dist_poisson ' +\n      '$dist_t $dist_uniform $q_initialize $q_remove $q_exam $async$and$array ' +\n      '$async$nand$array $async$or$array $async$nor$array $sync$and$array ' +\n      '$sync$nand$array $sync$or$array $sync$nor$array $q_add $q_full $psprintf ' +\n      '$async$and$plane $async$nand$plane $async$or$plane $async$nor$plane ' +\n      '$sync$and$plane $sync$nand$plane $sync$or$plane $sync$nor$plane $system ' +\n      '$display $displayb $displayh $displayo $strobe $strobeb $strobeh $strobeo ' +\n      '$write $readmemb $readmemh $writememh $value$plusargs ' +\n      '$dumpvars $dumpon $dumplimit $dumpports $dumpportson $dumpportslimit ' +\n      '$writeb $writeh $writeo $monitor $monitorb $monitorh $monitoro $writememb ' +\n      '$dumpfile $dumpoff $dumpall $dumpflush $dumpportsoff $dumpportsall ' +\n      '$dumpportsflush $fclose $fdisplay $fdisplayb $fdisplayh $fdisplayo ' +\n      '$fstrobe $fstrobeb $fstrobeh $fstrobeo $swrite $swriteb $swriteh ' +\n      '$swriteo $fscanf $fread $fseek $fflush $feof $fopen $fwrite $fwriteb ' +\n      '$fwriteh $fwriteo $fmonitor $fmonitorb $fmonitorh $fmonitoro $sformat ' +\n      '$sformatf $fgetc $ungetc $fgets $sscanf $rewind $ftell $ferror'\n  };\n\n  return {\n    name: 'Verilog',\n    aliases: [\n      'v',\n      'sv',\n      'svh'\n    ],\n    case_insensitive: false,\n    keywords: SV_KEYWORDS,\n    contains: [\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'number',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        variants: [\n          {\n            begin: '\\\\b((\\\\d+\\'(b|h|o|d|B|H|O|D))[0-9xzXZa-fA-F_]+)'\n          },\n          {\n            begin: '\\\\B((\\'(b|h|o|d|B|H|O|D))[0-9xzXZa-fA-F_]+)'\n          },\n          {\n            begin: '\\\\b([0-9_])+',\n            relevance: 0\n          }\n        ]\n      },\n      /* parameters to instances */\n      {\n        className: 'variable',\n        variants: [\n          {\n            begin: '#\\\\((?!parameter).+\\\\)'\n          },\n          {\n            begin: '\\\\.\\\\w+',\n            relevance: 0\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '`',\n        end: '$',\n        keywords: {\n          'meta-keyword':\n            'define __FILE__ ' +\n            '__LINE__ begin_keywords celldefine default_nettype define ' +\n            'else elsif end_keywords endcelldefine endif ifdef ifndef ' +\n            'include line nounconnected_drive pragma resetall timescale ' +\n            'unconnected_drive undef undefineall'\n        },\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = verilog;\n"], "names": [], "sourceRoot": ""}