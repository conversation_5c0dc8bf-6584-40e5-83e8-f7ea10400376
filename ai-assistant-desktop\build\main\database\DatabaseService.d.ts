import type { Conversation, Message, AppSettings } from '@shared/types';
export declare class DatabaseService {
    private db;
    private dbPath;
    constructor();
    initialize(): Promise<void>;
    private createTables;
    private initializeDefaultSettings;
    createConversation(data: {
        title: string;
        metadata?: Record<string, any>;
    }): Promise<Conversation>;
    getConversation(id: string): Promise<Conversation | null>;
    getConversations(limit?: number, offset?: number): Promise<Conversation[]>;
    updateConversation(id: string, updates: {
        title?: string;
        metadata?: Record<string, any>;
    }): Promise<boolean>;
    deleteConversation(id: string): Promise<boolean>;
    addMessage(conversationId: string, message: Omit<Message, 'id'>): Promise<Message>;
    updateMessage(id: string, updates: {
        content?: string;
        metadata?: Record<string, any>;
    }): Promise<boolean>;
    getSettings(): AppSettings;
    updateSettings(settings: Partial<AppSettings>): Promise<void>;
    getSetting(key: string): Promise<any>;
    setSetting(key: string, value: any): Promise<void>;
    cacheToolResult(toolName: string, args: Record<string, any>, result: any, success: boolean, ttl?: number): Promise<void>;
    getCachedToolResult(toolName: string, args: Record<string, any>): Promise<{
        result: any;
        success: boolean;
    } | null>;
    cacheFileContent(filePath: string, content: string, metadata?: Record<string, any>): Promise<void>;
    getCachedFileContent(filePath: string): Promise<{
        content: string;
        metadata: Record<string, any>;
    } | null>;
    log(level: 'debug' | 'info' | 'warn' | 'error', message: string, metadata?: Record<string, any>, source?: string): Promise<void>;
    getLogs(options?: {
        level?: string;
        source?: string;
        limit?: number;
        offset?: number;
        since?: Date;
    }): Promise<any[]>;
    cleanupExpiredCache(): Promise<void>;
    vacuum(): Promise<void>;
    private setupMaintenance;
    close(): Promise<void>;
    private generateId;
    private hashString;
    private hashObject;
    private flattenObject;
    private setNestedValue;
}
//# sourceMappingURL=DatabaseService.d.ts.map