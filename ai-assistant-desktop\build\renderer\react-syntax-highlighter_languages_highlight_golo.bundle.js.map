{"version": 3, "file": "react-syntax-highlighter_languages_highlight_golo.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/golo.js"], "sourcesContent": ["/*\nLanguage: Golo\nAuthor: <PERSON> <<EMAIL>>\nDescription: a lightweight dynamic language for the JVM\nWebsite: http://golo-lang.org/\n*/\n\nfunction golo(hljs) {\n  return {\n    name: 'Golo',\n    keywords: {\n      keyword:\n          'println readln print import module function local return let var ' +\n          'while for foreach times in case when match with break continue ' +\n          'augment augmentation each find filter reduce ' +\n          'if then else otherwise try catch finally raise throw orIfNull ' +\n          'DynamicObject|10 DynamicVariable struct Observable map set vector list array',\n      literal:\n          'true false null'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = golo;\n"], "names": [], "sourceRoot": ""}