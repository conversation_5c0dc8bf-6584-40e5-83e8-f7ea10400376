{"version": 3, "file": "SystemService.d.ts", "sourceRoot": "", "sources": ["../../../src/main/services/SystemService.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAIhD,qBAAa,aAAa;IAClB,aAAa,IAAI,OAAO,CAAC,UAAU,CAAC;IAUpC,cAAc,CAClB,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC9B,GACA,OAAO,CAAC;QACT,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IAiCI,uBAAuB,CAC3B,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7B,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;QAClC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;QAClC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;KACjC,GACA,OAAO,CAAC;QACT,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IAyDF,uBAAuB,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIjD,sBAAsB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAIxD,0BAA0B,IAAI,MAAM;IAIpC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAI1C,cAAc,IAAI;QAChB,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC;QAChC,MAAM,EAAE,MAAM,CAAC;KAChB;IAYK,kBAAkB,IAAI,OAAO,CAAC;QAClC,IAAI,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,EAAE;YACN,KAAK,EAAE,MAAM,CAAC;YACd,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;SACf,CAAC;QACF,IAAI,EAAE,MAAM,EAAE,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IAmBI,oBAAoB,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,CAAC,CAAC;IAI1E,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,GAAE,MAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAStE,aAAa,IAAI,OAAO,CAAC,KAAK,CAAC;QACnC,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,MAAM,CAAC;QACb,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,CAAC,CAAC;IAmEG,aAAa,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAMlC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAepD,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,SAAc,GAAG,OAAO,CAAC,OAAO,CAAC;IAyBjE,WAAW,IAAI,OAAO,CAAC;QAC3B,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC;IAUI,YAAY,CAAC,IAAI,GAAE,MAAY,GAAG,OAAO,CAAC;QAC9C,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;KACf,GAAG,IAAI,CAAC;CA+DV"}