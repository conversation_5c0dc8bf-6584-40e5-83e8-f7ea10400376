{"version": 3, "file": "react-syntax-highlighter_languages_highlight_vbscript.bundle.js", "mappings": ";;;;;;;;AAAA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;;AAEA;AACA,WAAW,kBAAkB;AAC7B,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,WAAW,uBAAuB;AAClC,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,sBAAsB;AACjC,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,4CAA4C,YAAY,YAAY,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/vbscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  const BUILT_IN_FUNCTIONS = ('lcase month vartype instrrev ubound setlocale getobject rgb getref string ' +\n  'weekdayname rnd dateadd monthname now day minute isarray cbool round formatcurrency ' +\n  'conversions csng timevalue second year space abs clng timeserial fixs len asc ' +\n  'isempty maths dateserial atn timer isobject filter weekday datevalue ccur isdate ' +\n  'instr datediff formatdatetime replace isnull right sgn array snumeric log cdbl hex ' +\n  'chr lbound msgbox ucase getlocale cos cdate cbyte rtrim join hour oct typename trim ' +\n  'strcomp int createobject loadpicture tan formatnumber mid ' +\n  'split  cint sin datepart ltrim sqr ' +\n  'time derived eval date formatpercent exp inputbox left ascw ' +\n  'chrw regexp cstr err').split(\" \");\n  const BUILT_IN_OBJECTS = [\n    \"server\",\n    \"response\",\n    \"request\",\n    // take no arguments so can be called without ()\n    \"scriptengine\",\n    \"scriptenginebuildversion\",\n    \"scriptengineminorversion\",\n    \"scriptenginemajorversion\"\n  ];\n\n  const BUILT_IN_CALL = {\n    begin: concat(either(...BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance:0,\n    keywords: {\n      built_in: BUILT_IN_FUNCTIONS\n    }\n  };\n\n  return {\n    name: 'VBScript',\n    aliases: ['vbs'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'call class const dim do loop erase execute executeglobal exit for each next function ' +\n        'if then else on error option explicit new private property let get public randomize ' +\n        'redim rem select case set stop sub while wend with end to elseif is or xor and not ' +\n        'class_initialize class_terminate default preserve in me byval byref step resume goto',\n      built_in: BUILT_IN_OBJECTS,\n      literal:\n        'true false null nothing empty'\n    },\n    illegal: '//',\n    contains: [\n      BUILT_IN_CALL,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {contains: [{begin: '\"\"'}]}),\n      hljs.COMMENT(\n        /'/,\n        /$/,\n        {\n          relevance: 0\n        }\n      ),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = vbscript;\n"], "names": [], "sourceRoot": ""}