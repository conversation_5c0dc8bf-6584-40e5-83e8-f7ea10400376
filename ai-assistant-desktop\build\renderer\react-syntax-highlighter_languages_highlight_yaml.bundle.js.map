{"version": 3, "file": "react-syntax-highlighter_languages_highlight_yaml.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,uCAAuC;AAC/C,QAAQ,yCAAyC;AACjD,QAAQ,4CAA4C;AACpD;AACA;;AAEA;AACA;AACA;AACA,QAAQ,UAAU,EAAE,WAAW,EAAE,GAAG;AACpC,QAAQ,WAAW,WAAW,IAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,sBAAsB;AAC9B,QAAQ,sBAAsB;AAC9B,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAQ,sBAAsB;AAC9B,QAAQ,sBAAsB;AAC9B,QAAQ,eAAe;AACvB;AACA,GAAG;;AAEH,uBAAuB,EAAE,cAAc,IAAI;AAC3C,mDAAmD,EAAE;AACrD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA,KAAK;AACL;AACA,MAAM;AACN;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,kBAAkB;AAClB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/yaml.js"], "sourcesContent": ["/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  var LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  var URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  var KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' }, // double quoted keys\n      { begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' } // single quoted keys\n    ]\n  };\n\n  var TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { begin: /\\{\\{/, end: /\\}\\}/ }, // jinja templates Ansible\n      { begin: /%\\{/, end: /\\}/ } // Ruby i18n\n    ]\n  };\n  var STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  var CONTAINER_STRING = hljs.inherit(STRING, {\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /[^\\s,{}[\\]]+/ }\n    ]\n  });\n\n  var DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  var TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  var FRACTION_RE = '(\\\\.[0-9]*)?';\n  var ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  var TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  var VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  var OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  var ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  var MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  var VALUE_MODES = [...MODES];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nmodule.exports = yaml;\n"], "names": [], "sourceRoot": ""}