import type { ToolSchema } from '@shared/types';
export declare const category: "system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    command: string;
    workingDirectory?: string;
    timeout?: number;
    shell?: string;
}): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
    command: string;
    executionTime: number;
}>;
export declare function executeStreaming(args: {
    command: string;
    workingDirectory?: string;
    shell?: string;
    onStdout?: (data: string) => void;
    onStderr?: (data: string) => void;
}): Promise<{
    exitCode: number;
    command: string;
    executionTime: number;
}>;
//# sourceMappingURL=run_shell_command.d.ts.map