{"version": 3, "file": "react-syntax-highlighter_languages_highlight_haskell.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA,cAAc;AACd,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/haskell.js"], "sourcesContent": ["/*\nLanguage: Haskell\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.haskell.org\nCategory: functional\n*/\n\nfunction haskell(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const PRAGMA = {\n    className: 'meta',\n    begin: /\\{-#/,\n    end: /#-\\}/\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '^#',\n    end: '$'\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (build-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      PRAGMA,\n      PREPROCESSOR,\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '[_a-z][\\\\w\\']*'\n      }),\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  return {\n    name: 'Haskell',\n    aliases: ['hs'],\n    keywords:\n      'let in if then else case of where do module import hiding ' +\n      'qualified type data newtype deriving class instance as default ' +\n      'infix infixl infixr foreign export ccall stdcall cplusplus ' +\n      'jvm dotnet safe unsafe family forall mdo proc rec',\n    contains: [\n      // Top-level constructions.\n      {\n        beginKeywords: 'module',\n        end: 'where',\n        keywords: 'module where',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: '\\\\bimport\\\\b',\n        end: '$',\n        keywords: 'import qualified as hiding',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        className: 'class',\n        begin: '^(\\\\s*)?(class|instance)\\\\b',\n        end: 'where',\n        keywords: 'class family instance where',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        className: 'class',\n        begin: '\\\\b(data|(new)?type)\\\\b',\n        end: '$',\n        keywords: 'data family type newtype deriving',\n        contains: [\n          PRAGMA,\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'default',\n        end: '$',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: '\\\\bforeign\\\\b',\n        end: '$',\n        keywords: 'foreign import export ccall stdcall cplusplus jvm ' +\n                  'dotnet safe unsafe',\n        contains: [\n          CONSTRUCTOR,\n          hljs.QUOTE_STRING_MODE,\n          COMMENT\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '#!\\\\/usr\\\\/bin\\\\/env\\ runhaskell',\n        end: '$'\n      },\n      // \"Whitespaces\".\n      PRAGMA,\n      PREPROCESSOR,\n\n      // Literals and names.\n\n      // TODO: characters.\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n      { // No markup, relevance booster\n        begin: '->|<-'\n      }\n    ]\n  };\n}\n\nmodule.exports = haskell;\n"], "names": [], "sourceRoot": ""}