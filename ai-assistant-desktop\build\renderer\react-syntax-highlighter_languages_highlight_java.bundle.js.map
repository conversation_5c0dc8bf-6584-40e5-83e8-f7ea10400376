{"version": 3, "file": "react-syntax-highlighter_languages_highlight_java.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA,kBAAkB,cAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe,cAAc,KAAK,KAAK,WAAW,KAAK;AAC7D,mBAAmB,cAAc,cAAc;AAC/C;AACA,MAAM,cAAc,cAAc,KAAK,KAAK,+BAA+B;AAC3E,MAAM,WAAW,KAAK,cAAc;AACpC,MAAM,cAAc,cAAc,aAAa;;AAE/C;AACA,MAAM,oBAAoB,UAAU,SAAS,UAAU,QAAQ,UAAU;AACzE,mBAAmB,cAAc,cAAc;;AAE/C;AACA,MAAM,yCAAyC;;AAE/C;AACA,MAAM,mBAAmB,UAAU,YAAY;;AAE/C;AACA,MAAM,iCAAiC;;AAEvC;AACA,MAAM,wCAAwC;AAC9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,qCAAqC;AACjD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,YAAY,yBAAyB;AACrC;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,qHAAqH;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/java.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\nfunction java(hljs) {\n  var JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  var GENERIC_IDENT_RE = JAVA_IDENT_RE + '(<' + JAVA_IDENT_RE + '(\\\\s*,\\\\s*' + JAVA_IDENT_RE + ')*>)?';\n  var KEYWORDS = 'false synchronized int abstract float private char boolean var static null if const ' +\n    'for true while long strictfp finally protected import native final void ' +\n    'enum else break transient catch instanceof byte super volatile case assert short ' +\n    'package default double public try this switch continue throws protected public private ' +\n    'module requires exports do';\n\n  var ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\"self\"] // allow nested () inside our annotation\n      },\n    ]\n  };\n  const NUMBER = NUMERIC;\n\n  return {\n    name: 'Java',\n    aliases: ['jsp'],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/, relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'class interface enum', end: /[{;=]/, excludeEnd: true,\n        // TODO: can this be removed somehow?\n        // an extra boost because Java is more popular than other languages with\n        // this same syntax feature (this is just to preserve our tests passing\n        // for now)\n        relevance: 1,\n        keywords: 'class interface enum',\n        illegal: /[:\"\\[\\]]/,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        className: 'class',\n        begin: 'record\\\\s+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n        returnBegin: true,\n        excludeEnd: true,\n        end: /[{;=]/,\n        keywords: KEYWORDS,\n        contains: [\n          { beginKeywords: \"record\" },\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'function',\n        begin: '(' + GENERIC_IDENT_RE + '\\\\s+)+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true, end: /[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMBER,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMBER,\n      ANNOTATION\n    ]\n  };\n}\n\nmodule.exports = java;\n"], "names": [], "sourceRoot": ""}