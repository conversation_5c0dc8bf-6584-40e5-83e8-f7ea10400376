{"version": 3, "file": "react-syntax-highlighter_languages_highlight_haxe.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,qBAAqB;AACrB,WAAW;AACX;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,iBAAiB;AACjB;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/haxe.js"], "sourcesContent": ["/*\nLanguage: Haxe\nDescription: Haxe is an open source toolkit based on a modern, high level, strictly typed programming language.\nAuthor: <PERSON> <i<PERSON><PERSON>@gmail.com> (Based on the actionscript.js language file by <PERSON>)\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://haxe.org\n*/\n\nfunction haxe(hljs) {\n\n  const HAXE_BASIC_TYPES = 'Int Float String Bool Dynamic Void Array ';\n\n  return {\n    name: 'Haxe',\n    aliases: ['hx'],\n    keywords: {\n      keyword: 'break case cast catch continue default do dynamic else enum extern ' +\n               'for function here if import in inline never new override package private get set ' +\n               'public return static super switch this throw trace try typedef untyped using var while ' +\n               HAXE_BASIC_TYPES,\n      built_in:\n        'trace this',\n      literal:\n        'true false null _'\n    },\n    contains: [\n      {\n        className: 'string', // interpolate-able strings\n        begin: '\\'',\n        end: '\\'',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$\\\\{',\n            end: '\\\\}'\n          },\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$',\n            end: /\\W\\}/\n          }\n        ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta', // compiler meta\n        begin: '@:',\n        end: '$'\n      },\n      {\n        className: 'meta', // compiler conditionals\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif end error'\n        }\n      },\n      {\n        className: 'type', // function types\n        begin: ':[ \\t]*',\n        end: '[^A-Za-z0-9_ \\t\\\\->]',\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        className: 'type', // types\n        begin: ':[ \\t]*',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'type', // instantiation\n        begin: 'new *',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'class', // enums\n        beginKeywords: 'enum',\n        end: '\\\\{',\n        contains: [hljs.TITLE_MODE]\n      },\n      {\n        className: 'class', // abstracts\n        beginKeywords: 'abstract',\n        end: '[\\\\{$]',\n        contains: [\n          {\n            className: 'type',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'from +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'to +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          hljs.TITLE_MODE\n        ],\n        keywords: {\n          keyword: 'abstract from to'\n        }\n      },\n      {\n        className: 'class', // classes\n        begin: '\\\\b(class|interface) +',\n        end: '[\\\\{$]',\n        excludeEnd: true,\n        keywords: 'class interface',\n        contains: [\n          {\n            className: 'keyword',\n            begin: '\\\\b(extends|implements) +',\n            keywords: 'extends implements',\n            contains: [\n              {\n                className: 'type',\n                begin: hljs.IDENT_RE,\n                relevance: 0\n              }\n            ]\n          },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\(',\n        excludeEnd: true,\n        illegal: '\\\\S',\n        contains: [hljs.TITLE_MODE]\n      }\n    ],\n    illegal: /<\\//\n  };\n}\n\nmodule.exports = haxe;\n"], "names": [], "sourceRoot": ""}