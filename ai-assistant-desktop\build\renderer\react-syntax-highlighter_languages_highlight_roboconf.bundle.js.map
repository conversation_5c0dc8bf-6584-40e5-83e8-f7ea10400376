{"version": 3, "file": "react-syntax-highlighter_languages_highlight_roboconf.bundle.js", "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sCAAsC,KAAK;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/roboconf.js"], "sourcesContent": ["/*\nLanguage: Roboconf\nAuthor: <PERSON> <v<PERSON><PERSON><PERSON>@linagora.com>\nDescription: Syntax highlighting for Roboconf's DSL\nWebsite: http://roboconf.net\nCategory: config\n*/\n\nfunction roboconf(hljs) {\n  const IDENTIFIER = '[a-zA-Z-_][^\\\\n{]+\\\\{';\n\n  const PROPERTY = {\n    className: 'attribute',\n    begin: /[a-zA-Z-_]+/,\n    end: /\\s*:/,\n    excludeEnd: true,\n    starts: {\n      end: ';',\n      relevance: 0,\n      contains: [\n        {\n          className: 'variable',\n          begin: /\\.[a-zA-Z-_]+/\n        },\n        {\n          className: 'keyword',\n          begin: /\\(optional\\)/\n        }\n      ]\n    }\n  };\n\n  return {\n    name: 'Roboconf',\n    aliases: [\n      'graph',\n      'instances'\n    ],\n    case_insensitive: true,\n    keywords: 'import',\n    contains: [\n      // Facet sections\n      {\n        begin: '^facet ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'facet',\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Instance sections\n      {\n        begin: '^\\\\s*instance of ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'name count channels instance-data instance-state instance of',\n        illegal: /\\S/,\n        contains: [\n          'self',\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Component sections\n      {\n        begin: '^' + IDENTIFIER,\n        end: /\\}/,\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Comments\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = roboconf;\n"], "names": [], "sourceRoot": ""}