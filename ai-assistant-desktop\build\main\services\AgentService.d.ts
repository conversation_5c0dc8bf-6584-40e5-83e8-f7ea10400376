import type { Message, Agent<PERSON><PERSON>, ToolResult, AgentState } from '@shared/types';
import { LLMService } from './LLMService';
import { ToolRegistry } from './ToolRegistry';
export declare class AgentService {
    private llmService;
    private toolRegistry;
    private currentState;
    private conversations;
    constructor(llmService: LLMService, toolRegistry: ToolRegistry);
    listen(input: {
        conversationId: string;
        message: string;
        context?: {
            files?: string[];
            codeSymbols?: string[];
            systemInfo?: boolean;
        };
    }): Promise<Message>;
    createPlan(request: {
        conversationId: string;
        executionMode?: 'confirm' | 'yolo';
        maxSteps?: number;
    }): Promise<{
        success: boolean;
        plan?: AgentPlan;
        error?: string;
    }>;
    confirmExecution(request: {
        conversationId: string;
        approved: boolean;
        modifiedPlan?: AgentPlan;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    executePlan(request: {
        conversationId: string;
        stepId?: string;
    }): Promise<{
        success: boolean;
        results?: ToolResult[];
        nextStep?: string;
        completed?: boolean;
        error?: string;
    }>;
    observe(request: {
        conversationId: string;
        results: ToolResult[];
        userQuestion?: string;
    }): Promise<{
        success: boolean;
        response?: string;
        error?: string;
    }>;
    private parsePlanFromResponse;
    private validatePlan;
    private areDependenciesSatisfied;
    getCurrentState(): AgentState;
    getConversation(conversationId: string): Message[] | undefined;
    clearState(): void;
    setExecutionMode(mode: 'confirm' | 'yolo'): void;
}
//# sourceMappingURL=AgentService.d.ts.map