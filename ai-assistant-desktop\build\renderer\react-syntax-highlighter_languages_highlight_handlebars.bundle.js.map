{"version": 3, "file": "react-syntax-highlighter_languages_highlight_handlebars.bundle.js", "mappings": ";;;;;;;;AAAA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;;AAEA;AACA,WAAW,kBAAkB;AAC7B,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,WAAW,kBAAkB;AAC7B,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA,WAAW,kBAAkB;AAC7B,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA,WAAW,uBAAuB;AAClC,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,sBAAsB;AACjC,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,6CAA6C,aAAa,EAAE;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iCAAiC,kBAAkB,KAAK,OAAO;AAC/D;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;;AAEA;AACA;AACA;AACA;AACA,cAAc,EAAE;AAChB,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,cAAc,EAAE;AAChB,KAAK;AACL,GAAG;;AAEH;AACA,gBAAgB,EAAE;AAClB;AACA;AACA;AACA,qBAAqB,EAAE;AACvB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,EAAE,WAAW,EAAE;AACrC,sBAAsB,EAAE,OAAO,EAAE;AACjC;AACA,+BAA+B,QAAQ,0BAA0B,QAAQ;AACzE;AACA,kBAAkB,EAAE,EAAE,EAAE;AACxB,gBAAgB,EAAE,EAAE,EAAE;AACtB;AACA;AACA,kBAAkB,EAAE,EAAE,EAAE;AACxB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB,EAAE,EAAE,EAAE;AACxB,gBAAgB,EAAE,EAAE,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB,EAAE;AACpB,gBAAgB,EAAE;AAClB;AACA,OAAO;AACP;AACA;AACA,kBAAkB,EAAE,SAAS,EAAE;AAC/B,gBAAgB,EAAE;AAClB;AACA,OAAO;AACP;AACA;AACA,kBAAkB,EAAE;AACpB,gBAAgB,EAAE;AAClB;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB,EAAE;AACpB,gBAAgB,EAAE;AAClB;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB,EAAE,EAAE;AACtB,gBAAgB,EAAE,EAAE;AACpB;AACA,OAAO;AACP;AACA;AACA;AACA,kBAAkB,EAAE;AACpB,gBAAgB,EAAE;AAClB;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://ai-assistant-desktop/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/handlebars.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Handlebars\nRequires: xml.js\nAuthor: <PERSON> <PERSON> <<EMAIL>>\nDescription: Matcher for Handlebars as well as EmberJS additions.\nWebsite: https://handlebarsjs.com\nCategory: template\n*/\n\nfunction handlebars(hljs) {\n  const BUILT_INS = {\n    'builtin-name': [\n      'action',\n      'bindattr',\n      'collection',\n      'component',\n      'concat',\n      'debugger',\n      'each',\n      'each-in',\n      'get',\n      'hash',\n      'if',\n      'in',\n      'input',\n      'link-to',\n      'loc',\n      'log',\n      'lookup',\n      'mut',\n      'outlet',\n      'partial',\n      'query-params',\n      'render',\n      'template',\n      'textarea',\n      'unbound',\n      'unless',\n      'view',\n      'with',\n      'yield'\n    ]\n  };\n\n  const LITERALS = {\n    literal: [\n      'true',\n      'false',\n      'undefined',\n      'null'\n    ]\n  };\n\n  // as defined in https://handlebarsjs.com/guide/expressions.html#literal-segments\n  // this regex matches literal segments like ' abc ' or [ abc ] as well as helpers and paths\n  // like a/b, ./abc/cde, and abc.bcd\n\n  const DOUBLE_QUOTED_ID_REGEX = /\"\"|\"[^\"]+\"/;\n  const SINGLE_QUOTED_ID_REGEX = /''|'[^']+'/;\n  const BRACKET_QUOTED_ID_REGEX = /\\[\\]|\\[[^\\]]+\\]/;\n  const PLAIN_ID_REGEX = /[^\\s!\"#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]+/;\n  const PATH_DELIMITER_REGEX = /(\\.|\\/)/;\n  const ANY_ID = either(\n    DOUBLE_QUOTED_ID_REGEX,\n    SINGLE_QUOTED_ID_REGEX,\n    BRACKET_QUOTED_ID_REGEX,\n    PLAIN_ID_REGEX\n    );\n\n  const IDENTIFIER_REGEX = concat(\n    optional(/\\.|\\.\\/|\\//), // relative or absolute path\n    ANY_ID,\n    anyNumberOfTimes(concat(\n      PATH_DELIMITER_REGEX,\n      ANY_ID\n    ))\n  );\n\n  // identifier followed by a equal-sign (without the equal sign)\n  const HASH_PARAM_REGEX = concat(\n    '(',\n    BRACKET_QUOTED_ID_REGEX, '|',\n    PLAIN_ID_REGEX,\n    ')(?==)'\n  );\n\n  const HELPER_NAME_OR_PATH_EXPRESSION = {\n    begin: IDENTIFIER_REGEX,\n    lexemes: /[\\w.\\/]+/\n  };\n\n  const HELPER_PARAMETER = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: LITERALS\n  });\n\n  const SUB_EXPRESSION = {\n    begin: /\\(/,\n    end: /\\)/\n    // the \"contains\" is added below when all necessary sub-modes are defined\n  };\n\n  const HASH = {\n    // fka \"attribute-assignment\", parameters of the form 'key=value'\n    className: 'attr',\n    begin: HASH_PARAM_REGEX,\n    relevance: 0,\n    starts: {\n      begin: /=/,\n      end: /=/,\n      starts: {\n        contains: [\n          hljs.NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          HELPER_PARAMETER,\n          SUB_EXPRESSION\n        ]\n      }\n    }\n  };\n\n  const BLOCK_PARAMS = {\n    // parameters of the form '{{#with x as | y |}}...{{/with}}'\n    begin: /as\\s+\\|/,\n    keywords: {\n      keyword: 'as'\n    },\n    end: /\\|/,\n    contains: [\n      {\n        // define sub-mode in order to prevent highlighting of block-parameter named \"as\"\n        begin: /\\w+/\n      }\n    ]\n  };\n\n  const HELPER_PARAMETERS = {\n    contains: [\n      hljs.NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      BLOCK_PARAMS,\n      HASH,\n      HELPER_PARAMETER,\n      SUB_EXPRESSION\n    ],\n    returnEnd: true\n    // the property \"end\" is defined through inheritance when the mode is used. If depends\n    // on the surrounding mode, but \"endsWithParent\" does not work here (i.e. it includes the\n    // end-token of the surrounding mode)\n  };\n\n  const SUB_EXPRESSION_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\)/\n    })\n  });\n\n  SUB_EXPRESSION.contains = [SUB_EXPRESSION_CONTENTS];\n\n  const OPENING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name',\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const CLOSING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name'\n  });\n\n  const BASIC_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\{\\{/,\n    skip: true\n  };\n  const PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\\\(?=\\{\\{)/,\n    skip: true\n  };\n\n  return {\n    name: 'Handlebars',\n    aliases: [\n      'hbs',\n      'html.hbs',\n      'html.handlebars',\n      'htmlbars'\n    ],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH,\n      PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH,\n      hljs.COMMENT(/\\{\\{!--/, /--\\}\\}/),\n      hljs.COMMENT(/\\{\\{!/, /\\}\\}/),\n      {\n        // open raw block \"{{{{raw}}}} content not evaluated {{{{/raw}}}}\"\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{(?!\\/)/,\n        end: /\\}\\}\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS],\n        starts: {\n          end: /\\{\\{\\{\\{\\//,\n          returnEnd: true,\n          subLanguage: 'xml'\n        }\n      },\n      {\n        // close raw block\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{\\//,\n        end: /\\}\\}\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // open block statement\n        className: 'template-tag',\n        begin: /\\{\\{#/,\n        end: /\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else\\}\\})/,\n        end: /\\}\\}/,\n        keywords: 'else'\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else if)/,\n        end: /\\}\\}/,\n        keywords: 'else if'\n      },\n      {\n        // closing block statement\n        className: 'template-tag',\n        begin: /\\{\\{\\//,\n        end: /\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is NOT html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{\\{/,\n        end: /\\}\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{/,\n        end: /\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      }\n    ]\n  };\n}\n\nmodule.exports = handlebars;\n"], "names": [], "sourceRoot": ""}